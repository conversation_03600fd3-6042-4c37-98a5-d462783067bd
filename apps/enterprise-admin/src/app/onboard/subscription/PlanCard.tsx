import React from 'react';
import { Plan } from './types';
import { <PERSON>, CardH<PERSON>er, CardT<PERSON>le, CardContent, CardFooter } from '@b6ai/ui';
import { Button } from '@b6ai/ui';

interface PlanCardProps {
  plan: Plan;
  isYearly: boolean;
  onSelect: (plan: Plan) => void;
}

const PlanCard: React.FC<PlanCardProps> = ({ plan, isYearly, onSelect }) => {
  const price = isYearly ? plan.yearlyPrice : plan.monthlyPrice;
  const period = isYearly ? '/year' : '/month';

  return (
    <Card className={plan.popular ? 'border-primary' : ''}>
      {plan.popular && (
        <div className="text-center text-primary font-semibold mb-2">
          Most Popular
        </div>
      )}
      <CardHeader>
        <CardTitle>{plan.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-4">
          ${price} <span className="text-sm font-normal">{period}</span>
        </div>
        <ul className="mb-6">
          {plan.features.map((feature, index) => (
            <li key={index} className="mb-2">
              • {feature}
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <Button onClick={() => onSelect(plan)} className="w-full">
          Select Plan
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PlanCard;
