import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Toggle from './Toggle';

describe('Toggle', () => {
  it('renders Monthly and Yearly buttons', () => {
    const mockOnToggle = jest.fn();
    render(<Toggle isYearly={true} onToggle={mockOnToggle} />);

    expect(screen.getByText('Monthly')).toBeTruthy();
    expect(screen.getByText('Yearly')).toBeTruthy();
  });

  it('calls onToggle with false when Monthly is clicked', () => {
    const mockOnToggle = jest.fn();
    render(<Toggle isYearly={true} onToggle={mockOnToggle} />);

    fireEvent.click(screen.getByText('Monthly'));
    expect(mockOnToggle).toHaveBeenCalledWith(false);
  });

  it('calls onToggle with true when Yearly is clicked', () => {
    const mockOnToggle = jest.fn();
    render(<Toggle isYearly={false} onToggle={mockOnToggle} />);

    fireEvent.click(screen.getByText('Yearly'));
    expect(mockOnToggle).toHaveBeenCalledWith(true);
  });
});
