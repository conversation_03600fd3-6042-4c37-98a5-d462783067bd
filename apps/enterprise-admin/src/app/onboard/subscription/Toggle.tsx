import React from 'react';
import { Button } from '@b6ai/ui';

interface ToggleProps {
  isYearly: boolean;
  onToggle: (isYearly: boolean) => void;
}

const Toggle: React.FC<ToggleProps> = ({ isYearly, onToggle }) => {
  return (
    <div className="flex items-center justify-center mb-8">
      <Button
        onClick={() => onToggle(false)}
        variant={!isYearly ? 'default' : 'outline'}
        className="rounded-r-none"
      >
        Monthly
      </Button>
      <Button
        onClick={() => onToggle(true)}
        variant={isYearly ? 'default' : 'outline'}
        className="rounded-l-none"
      >
        Yearly
      </Button>
    </div>
  );
};

export default Toggle;
