'use client';

import React, { useState } from 'react';
import { PlanToggle } from '../../../views/subscription/PlanToggle';
import { Plan, PlanType } from '@b6ai/shared';
import { PlanList } from '../../../views/subscription/PlansList';

export const dummyPlans: Plan[] = [
  {
    createdOn: '2024-01-15T10:00:00Z',
    modifiedOn: '2024-01-15T10:00:00Z',
    deleted: false,
    deletedOn: '',
    deletedBy: '',
    id: 'plan-basic-001',
    planCode: 'BASIC',
    name: 'Basic',
    description:
      'Perfect for individuals and small projects getting started with our platform.',
    priceCents: 900, // $9.00
    currency: 'USD',
    gatewayName: 'stripe',
    gatewayRefId: 'price_1234567890abcdef',
    planFeatures: [
      {
        id: 'pf-001',
        value: '5 Projects',
        featureId: 'feat-projects',
        planId: 'plan-basic-001',
        feature: {
          id: 'feat-projects',
          name: 'Up to 5 active projects',
          description: 'Create and manage projects',
          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'BASIC',
      },
      {
        id: 'pf-002',
        value: '10GB Storage',
        featureId: 'feat-storage',
        planId: 'plan-basic-001',
        feature: {
          id: 'feat-storage',
          name: '10GB cloud storage',
          description: 'Secure cloud storage for your files',
          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'BASIC',
      },
      {
        id: 'pf-003',
        value: 'Basic Support',
        featureId: 'feat-support',
        planId: 'plan-basic-001',
        feature: {
          id: 'feat-support',
          name: 'Email support',
          description: '48-hour response time',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'BASIC',
      },
      {
        id: 'pf-004',
        value: '2 Team Members',
        featureId: 'feat-team',
        planId: 'plan-basic-001',
        feature: {
          id: 'feat-team',
          name: 'Up to 2 team members',
          description: 'Collaborate with your team',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'BASIC',
      },
    ],
  },
  {
    createdOn: '2024-01-15T10:00:00Z',
    modifiedOn: '2024-01-15T10:00:00Z',
    deleted: false,
    deletedOn: '',
    deletedBy: '',
    id: 'plan-pro-001',
    planCode: 'PROFESSIONAL',
    name: 'Professional',
    description:
      'Advanced features for growing teams and businesses that need more power.',
    priceCents: 2900, // $29.00
    currency: 'USD',
    gatewayName: 'stripe',
    gatewayRefId: 'price_0987654321fedcba',
    planFeatures: [
      {
        id: 'pf-101',
        value: 'Unlimited Projects',
        featureId: 'feat-projects-pro',
        planId: 'plan-pro-001',
        feature: {
          id: 'feat-projects-pro',
          name: 'Unlimited active projects',
          description: 'No limits on project creation',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'PROFESSIONAL',
      },
      {
        id: 'pf-102',
        value: '100GB Storage',
        featureId: 'feat-storage-pro',
        planId: 'plan-pro-001',
        feature: {
          id: 'feat-storage-pro',
          name: '100GB cloud storage',
          description: 'Extended storage capacity',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'PROFESSIONAL',
      },
      {
        id: 'pf-103',
        value: 'Priority Support',
        featureId: 'feat-support-pro',
        planId: 'plan-pro-001',
        feature: {
          id: 'feat-support-pro',
          name: 'Priority email & chat support',
          description: '24-hour response time',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'PROFESSIONAL',
      },
      {
        id: 'pf-104',
        value: '10 Team Members',
        featureId: 'feat-team-pro',
        planId: 'plan-pro-001',
        feature: {
          id: 'feat-team-pro',
          name: 'Up to 10 team members',
          description: 'Extended team collaboration',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'PROFESSIONAL',
      },
      {
        id: 'pf-105',
        value: 'Advanced Analytics',
        featureId: 'feat-analytics',
        planId: 'plan-pro-001',
        feature: {
          id: 'feat-analytics',
          name: 'Advanced analytics & insights',
          description: 'Detailed reports and metrics',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'PROFESSIONAL',
      },
      {
        id: 'pf-106',
        value: 'API Access',
        featureId: 'feat-api',
        planId: 'plan-pro-001',
        feature: {
          id: 'feat-api',
          name: 'Full API access',
          description: 'Integrate with your tools',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'PROFESSIONAL',
      },
    ],
  },
  {
    createdOn: '2024-01-15T10:00:00Z',
    modifiedOn: '2024-01-15T10:00:00Z',
    deleted: false,
    deletedOn: '',
    deletedBy: '',
    id: 'plan-ent-001',
    planCode: 'ENTERPRISE',
    name: 'Enterprise',
    description:
      'Custom solutions for large organizations with advanced security and compliance needs.',
    priceCents: 9900, // $99.00 (starting price)
    currency: 'USD',
    gatewayName: 'stripe',
    gatewayRefId: 'price_enterprise_custom',
    planFeatures: [
      {
        id: 'pf-201',
        value: 'Everything in Pro',
        featureId: 'feat-all-pro',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-all-pro',
          name: 'All Professional features',
          description: 'Includes everything from Professional plan',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
      {
        id: 'pf-202',
        value: 'Unlimited Storage',
        featureId: 'feat-storage-ent',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-storage-ent',
          name: 'Unlimited cloud storage',
          description: 'No storage limits',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
      {
        id: 'pf-203',
        value: 'Dedicated Support',
        featureId: 'feat-support-ent',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-support-ent',
          name: '24/7 dedicated support',
          description: 'Phone, email, and chat with SLA',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
      {
        id: 'pf-204',
        value: 'Unlimited Team Members',
        featureId: 'feat-team-ent',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-team-ent',
          name: 'Unlimited team members',
          description: 'No limits on team size',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
      {
        id: 'pf-205',
        value: 'Custom Integrations',
        featureId: 'feat-integrations',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-integrations',
          name: 'Custom integrations',
          description: 'Tailored to your tech stack',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
      {
        id: 'pf-206',
        value: 'SSO & SAML',
        featureId: 'feat-sso',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-sso',
          name: 'Single Sign-On (SSO) & SAML',
          description: 'Enterprise authentication',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
      {
        id: 'pf-207',
        value: 'Compliance & Security',
        featureId: 'feat-compliance',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-compliance',
          name: 'SOC 2, GDPR compliance',
          description: 'Enterprise-grade security',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
      {
        id: 'pf-208',
        value: 'Custom Training',
        featureId: 'feat-training',
        planId: 'plan-ent-001',
        feature: {
          id: 'feat-training',
          name: 'Onboarding & training sessions',
          description: 'Personalized team training',

          createdOn: '2024-01-15T10:00:00Z',
          modifiedOn: '2024-01-15T10:00:00Z',
        },

        plan: 'ENTERPRISE',
      },
    ],
  },
];

const SubscriptionPage: React.FC = () => {
  const [billingPeriod, setBillingPeriod] = useState<PlanType>(PlanType.YEARLY);

  return (
    <div className=" bg-b6ai-gradient-bg w-full h-full page-x-padding page-top-padding min-h-screen">
      <div className="mb-8 w-full flex justify-center">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Choose your right plan!</h1>

          <div className="space-y-1">
            {/* Full width limited to max-w-md */}
            <p className="text-base font-normal max-w-md mx-auto">
              Choose from the best plans to find your perfect match.
            </p>

            {/* Shrink-to-fit + centered */}
            <p className="text-base font-normal w-fit mx-auto">
              Need more or less? Customize your subscription for a seamless fit.
            </p>
          </div>
        </div>
      </div>
      <div className="w-full flex justify-center">
        <PlanToggle selected={billingPeriod} onChange={setBillingPeriod} />
      </div>

      <div className="w-full flex justify-center mt-4">
        <div className=" max-w-7xl">
          <PlanList plans={dummyPlans} />
        </div>
      </div>
    </div>
  );
};
