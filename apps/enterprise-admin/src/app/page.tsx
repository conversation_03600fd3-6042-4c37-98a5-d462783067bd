'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Input,
} from '@b6ai/ui';

export default function Index() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto p-8">
        <div className="space-y-8">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              Welcome to Enterprise Admin 👋
            </h1>
            <p className="text-muted-foreground">
              Showcasing shadcn/ui components with centralized theming
            </p>
          </div>

          {/* Theme Toggle Section */}
          <Card>
            <CardHeader>
              <CardTitle>Theme Demo</CardTitle>
              <CardDescription>
                Toggle between light and dark themes to see the centralized
                theming in action
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <Button
                  onClick={() => {
                    document.documentElement.classList.remove('dark');
                  }}
                  variant="outline"
                >
                  Light Theme
                </Button>
                <Button
                  onClick={() => document.documentElement.classList.add('dark')}
                  variant="outline"
                >
                  Dark Theme
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Components Showcase */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Buttons</CardTitle>
                <CardDescription>Various button variants</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full">Default</Button>
                <Button variant="secondary" className="w-full">
                  Secondary
                </Button>
                <Button variant="outline" className="w-full">
                  Outline
                </Button>
                <Button variant="ghost" className="w-full">
                  Ghost
                </Button>
                <Button variant="destructive" className="w-full">
                  Destructive
                </Button>
                <Button className="w-full px-4 py-2 btn-b6ai rounded-md">
                  B6AI Gradient Button
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Inputs</CardTitle>
                <CardDescription>Form input examples</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Input placeholder="Enter your name" />
                <Input type="email" placeholder="Enter your email" />
                <Input type="password" placeholder="Enter password" />
                <Input disabled placeholder="Disabled input" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Card Example</CardTitle>
                <CardDescription>This is a card component</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Cards are versatile containers that can hold various types of
                  content. They work seamlessly with the centralized theming
                  system.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <B6AIButtonExamples />
    </div>
  );
}

function B6AIButtonExamples() {
  return (
    <div className="min-h-screen bg-b6ai-gradient-bg p-8">
      <div className="max-w-4xl mx-auto space-y-12">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-b6ai-gradient mb-2">
            B6AI Design System
          </h1>
          <p className="text-muted-foreground">
            Button Components & Theme Examples
          </p>
        </div>

        {/* Light/Dark Mode Toggle Preview */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Light Mode Preview */}
          <div className="p-6 bg-white rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900">
              Light Mode
            </h2>
            <div className="space-y-4">
              <button className="w-full px-4 py-2 btn-b6ai rounded-md">
                B6AI Gradient Button
              </button>

              <button className="w-full px-4 py-2 btn-b6ai-outline rounded-md">
                B6AI Outline Button
              </button>

              <button className="w-full px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
                Primary Button
              </button>

              <button className="w-full px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors">
                Secondary Button
              </button>

              <button className="w-full px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md transition-colors">
                Outlined Button (Fixed)
              </button>
            </div>
          </div>

          {/* Dark Mode Preview */}
          <div className="p-6 bg-slate-900 rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-white">Dark Mode</h2>
            <div className="space-y-4">
              <button className="w-full px-4 py-2 btn-b6ai rounded-md">
                B6AI Gradient Button
              </button>

              <button className="w-full px-4 py-2 btn-b6ai-outline rounded-md">
                B6AI Outline Button
              </button>

              <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-500 transition-colors">
                Primary Button (#041C91)
              </button>

              <button className="w-full px-4 py-2 bg-cyan-500 text-slate-900 rounded-md hover:bg-cyan-400 transition-colors">
                Secondary Button (#37B8E6)
              </button>

              <button className="w-full px-4 py-2 border border-slate-600 text-cyan-400 hover:bg-blue-600 hover:text-white rounded-md transition-colors">
                Outlined Button (Fixed)
              </button>
            </div>
          </div>
        </div>

        {/* Gradient Variations */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-center">
            Gradient Variations
          </h2>

          <div className="grid md:grid-cols-3 gap-4">
            <div className="p-6 bg-b6ai-gradient rounded-lg text-white">
              <h3 className="font-semibold mb-2">Full Gradient</h3>
              <p className="text-sm opacity-90">
                Main brand gradient with full opacity
              </p>
            </div>

            <div className="p-6 bg-b6ai-gradient-subtle rounded-lg border border-b6ai-gradient">
              <h3 className="font-semibold mb-2">Subtle Gradient</h3>
              <p className="text-sm text-muted-foreground">
                Low opacity for backgrounds
              </p>
            </div>

            <div className="p-6 bg-b6ai-gradient-bg rounded-lg border">
              <h3 className="font-semibold mb-2">Background Gradient</h3>
              <p className="text-sm text-muted-foreground">
                Very subtle for page backgrounds
              </p>
            </div>
          </div>
        </div>

        {/* Interactive Examples */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-center">
            Interactive Components
          </h2>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="p-6 bg-card rounded-lg shadow-md hover:shadow-b6ai-lg transition-shadow">
              <h3 className="font-semibold mb-2">Card with B6AI Shadow</h3>
              <p className="text-muted-foreground text-sm mb-4">
                Hover to see the branded shadow effect
              </p>
              <button className="btn-b6ai px-4 py-2 rounded-md text-sm">
                Learn More
              </button>
            </div>

            <div className="p-6 bg-card rounded-lg border-2 border-transparent hover:border-b6ai-gradient transition-all">
              <h3 className="font-semibold mb-2">Gradient Border on Hover</h3>
              <p className="text-muted-foreground text-sm mb-4">
                Interactive border animation
              </p>
              <button className="btn-b6ai-outline px-4 py-2 rounded-md text-sm">
                Explore
              </button>
            </div>
          </div>
        </div>

        {/* Color Palette */}
        <div className="space-y-6">
          <h2 className="text-2xl font-semibold text-center">Brand Colors</h2>

          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-full h-24 bg-b6ai-cyan rounded-lg shadow-md mb-2"></div>
              <p className="font-medium">B6AI Cyan</p>
              <p className="text-sm text-muted-foreground">#37B8E6</p>
            </div>

            <div className="text-center">
              <div className="w-full h-24 bg-b6ai-blue rounded-lg shadow-md mb-2"></div>
              <p className="font-medium">B6AI Blue</p>
              <p className="text-sm text-muted-foreground">#041C91</p>
            </div>

            <div className="text-center">
              <div className="w-full h-24 bg-b6ai-navy rounded-lg shadow-md mb-2"></div>
              <p className="font-medium">B6AI Navy</p>
              <p className="text-sm text-muted-foreground">#01033D</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
