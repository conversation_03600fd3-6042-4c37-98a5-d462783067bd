'use client';

import React, { useEffect, useState, createContext, useContext } from 'react';
import i18n from 'i18next';
import { initReactI18next, useTranslation } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import {
  I18N_KEYS,
  enTranslation,
  arTranslation,
  esTranslation,
  frTranslation,
  deTranslation,
  ptTranslation,
  zhTranslation,
  jaTranslation,
} from '@b6ai/shared';
// Define supported languages
export const LANGUAGES = {
  en: {
    name: 'English',
    dir: 'ltr',
    flag: '🇺🇸',
  },
  ar: {
    name: 'العربية',
    dir: 'rtl',
    flag: '🇦🇪',
  },
  es: {
    name: 'Español',
    dir: 'ltr',
    flag: '🇪🇸',
  },
  fr: {
    name: 'Français',
    dir: 'ltr',
    flag: '🇫🇷',
  },
  de: {
    name: '<PERSON><PERSON><PERSON>',
    dir: 'ltr',
    flag: '🇩🇪',
  },
  pt: {
    name: '<PERSON>ug<PERSON><PERSON><PERSON>',
    dir: 'ltr',
    flag: '🇵🇹',
  },
  zh: {
    name: '中文',
    dir: 'ltr',
    flag: '🇨🇳',
  },
  ja: {
    name: '日本語',
    dir: 'ltr',
    flag: '🇯🇵',
  },
};
// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslation,
      },
      ar: {
        translation: arTranslation,
      },
      es: {
        translation: esTranslation,
      },
      fr: {
        translation: frTranslation,
      },
      de: {
        translation: deTranslation,
      },
      pt: {
        translation: ptTranslation,
      },
      zh: {
        translation: zhTranslation,
      },
      ja: {
        translation: jaTranslation,
      },
    },
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false,
    },
    // Force English as the default language
    lng: 'en',
  });
// Create context

type LocalizationContextType = {
  currentLanguage: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
  dir: string;
};
const LocalizationContext = createContext<LocalizationContextType | undefined>(
  undefined
);
export const LocalizationProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { t } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState('en'); // Default to English
  const [dir, setDir] = useState(
    LANGUAGES[currentLanguage as keyof typeof LANGUAGES]?.dir || 'ltr'
  );
  const setLanguage = (lang: string) => {
    i18n.changeLanguage(lang).then(() => {
      setCurrentLanguage(lang);
      const direction = LANGUAGES[lang as keyof typeof LANGUAGES]?.dir || 'ltr';
      setDir(direction);
      document.documentElement.dir = direction;
      document.documentElement.lang = lang;
    });
  };
  useEffect(() => {
    // Set initial direction and language attributes
    const direction =
      LANGUAGES[currentLanguage as keyof typeof LANGUAGES]?.dir || 'ltr';
    document.documentElement.dir = direction;
    document.documentElement.lang = currentLanguage;
    setDir(direction);
  }, [currentLanguage]);
  return (
    <LocalizationContext.Provider
      value={{
        currentLanguage,
        setLanguage,
        t,
        dir,
      }}
    >
      {children}
    </LocalizationContext.Provider>
  );
};
// Custom hook to use localization
export const useLocalization = () => {
  const context = useContext(LocalizationContext);
  if (context === undefined) {
    throw new Error(
      'useLocalization must be used within a LocalizationProvider'
    );
  }
  return context;
};
// Export keys for convenience
export { I18N_KEYS };
