'use client';

import { useState, useEffect, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Eye, EyeOff } from 'lucide-react';
import { Button, Input } from '@b6ai/ui';
import { useLoginMutation, LoginRequest, I18N_KEYS } from '@b6ai/shared';

import { useLocalization } from '../../context/localization-context';
import { useToast } from '../../context/toast-context';

export default function LoginPage() {
  const router = useRouter();
  const { t } = useLocalization();
  const toast = useToast();

  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [login, { isLoading }] = useLoginMutation();

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    if (!formData.email.trim())
      newErrors.email = t(I18N_KEYS.AUTH.EMAIL_REQUIRED);
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))
      newErrors.email = t(I18N_KEYS.AUTH.INVALID_EMAIL);

    if (!formData.password)
      newErrors.password = t(I18N_KEYS.AUTH.PASSWORD_REQUIRED);

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      const payload: LoginRequest = {
        email: formData.email,
        password: formData.password,
      };
      await login(payload).unwrap();

      if (formData.rememberMe)
        localStorage.setItem('rememberedEmail', formData.email);
      else localStorage.removeItem('rememberedEmail');

      // toast.success(t(I18N_KEYS.AUTH.LOGIN_SUCCESS));
      router.push('/');
    } catch {
      toast.error(t(I18N_KEYS.AUTH.INVALID_CREDENTIALS));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));

    if (errors[name]) setErrors((prev) => ({ ...prev, [name]: '' }));
  };

  useEffect(() => {
    const rememberedEmail = localStorage.getItem('rememberedEmail');
    if (rememberedEmail) {
      setFormData((prev) => ({
        ...prev,
        email: rememberedEmail,
        rememberMe: true,
      }));
    }
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Content */}
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              {t(I18N_KEYS.AUTH.LOGIN_TITLE)}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              {t(I18N_KEYS.AUTH.LOGIN_DESCRIPTION)}
            </p>
          </div>

          <form onSubmit={handleSubmit}>
            {/* Email */}
            <div className="mb-6">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                {t(I18N_KEYS.AUTH.EMAIL)}
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder={t(I18N_KEYS.AUTH.EMAIL_PLACEHOLDER)}
                className={`w-full h-12 ${
                  errors.email
                    ? 'border-red-500'
                    : 'border-gray-300 dark:border-gray-600'
                }`}
              />
              {errors.email && (
                <p className="mt-2 text-sm text-red-500">{errors.email}</p>
              )}
            </div>

            {/* Password */}
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  {t(I18N_KEYS.AUTH.PASSWORD)}
                </label>
                <Link
                  href="/forgot-password"
                  className="text-sm text-b6ai-blue dark:text-b6ai-cyan hover:underline"
                >
                  {t(I18N_KEYS.AUTH.FORGOT_PASSWORD)}
                </Link>
              </div>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder={t(I18N_KEYS.AUTH.PASSWORD_PLACEHOLDER)}
                  className={`w-full h-12 pr-10 ${
                    errors.password
                      ? 'border-red-500'
                      : 'border-gray-300 dark:border-gray-600'
                  }`}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-b6ai-blue h-full"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-500">{errors.password}</p>
              )}
            </div>

            {/* Remember Me */}
            <div className="flex items-center mb-6">
              <input
                type="checkbox"
                id="remember-me"
                checked={formData.rememberMe}
                onChange={() =>
                  setFormData((prev) => ({
                    ...prev,
                    rememberMe: !prev.rememberMe,
                  }))
                }
                className="w-4 h-4 rounded border-gray-300 dark:border-gray-600 text-b6ai-blue dark:text-b6ai-cyan focus:ring-b6ai-blue dark:focus:ring-b6ai-cyan"
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                {t(I18N_KEYS.AUTH.REMEMBER_ME)}
              </label>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={isLoading}
              className={`w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-b6ai-blue to-b6ai-cyan text-white rounded-lg ${
                isLoading ? 'opacity-70 cursor-not-allowed' : 'hover:opacity-90'
              } transition-opacity`}
            >
              {isLoading
                ? t(I18N_KEYS.COMMON.LOADING)
                : t(I18N_KEYS.AUTH.LOGIN_BUTTON)}
            </Button>
          </form>

          {/* Sign Up */}
          <div className="mt-6 text-center text-sm text-gray-600 dark:text-gray-400">
            {t(I18N_KEYS.AUTH.NO_ACCOUNT)}{' '}
            <Link
              href="/onboard"
              className="text-b6ai-blue dark:text-b6ai-cyan hover:underline"
            >
              {t(I18N_KEYS.AUTH.SIGN_UP)}
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="py-6">
        <div className="container mx-auto px-6">
          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            © 2023 B6AI. {t(I18N_KEYS.COMMON.ALL_RIGHTS_RESERVED)}
          </p>
        </div>
      </footer>
    </div>
  );
}
