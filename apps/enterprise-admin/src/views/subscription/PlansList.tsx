import { Plan } from '@b6ai/shared';
import { cn } from '@b6ai/ui';
import { Check, Star } from 'lucide-react';

interface PlanListProps {
  plans: Plan[];
  popularPlanId?: string; // Optional: mark a plan as popular
  onGetStarted?: (plan: Plan) => void;
  onBookCall?: (plan: Plan) => void;
}

// Currency symbol mapping
const currencySymbols: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  JPY: '¥',
  CAD: 'C$',
  AUD: 'A$',
  INR: '₹',
  CNY: '¥',
  CHF: 'Fr',
  SEK: 'kr',
  NZD: 'NZ$',
  MXN: '$',
  SGD: 'S$',
  HKD: 'HK$',
  NOK: 'kr',
  KRW: '₩',
  BRL: 'R$',
  RUB: '₽',
  ZAR: 'R',
  AED: 'د.إ',
};

export function PlanList({
  plans,
  popularPlanId,
  onGetStarted,
  onBookCall,
}: PlanListProps) {
  const formatPrice = (priceCents: number, currency: string) => {
    const symbol = currencySymbols[currency] || currency;
    const price = (priceCents / 100).toFixed(2);
    // Remove .00 for cleaner look
    return `${symbol}${price.replace(/\.00$/, '')}`;
  };

  const handleAction = (plan: Plan) => {
    if (plan.planCode === 'ENTERPRISE' && onBookCall) {
      onBookCall(plan);
    } else if (onGetStarted) {
      onGetStarted(plan);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {plans.map((plan) => {
        const isEnterprise = plan.planCode === 'ENTERPRISE';
        const isPopular = plan.id === popularPlanId;

        return (
          <div
            key={plan.id}
            className={cn(
              'relative flex flex-col h-full rounded-3xl border shadow-md hover:shadow-b6ai-lg transition-shadow',
              isEnterprise
                ? 'bg-b6ai-gradient-subtle border-primary/20'
                : 'bg-card',
              isPopular && 'ring-2 ring-primary'
            )}
          >
            {/* Popular Badge */}
            {isPopular && (
              <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                <div className="flex items-center gap-1 bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium">
                  <Star className="w-3 h-3" />
                  Most Popular
                </div>
              </div>
            )}

            {/* Card Content */}
            <div className="flex-1 p-6 space-y-4">
              {/* Plan Name */}
              <div className="inline-flex bg-b6ai-gradient min-w-36 p-1 rounded-md">
                <h3 className="text-xl font-semibold px-3 py-1 text-white  rounded-md text-left">
                  {plan.name}
                </h3>
              </div>

              {/* Description */}
              <p className="text-muted-foreground text-sm leading-relaxed">
                {plan.description}
              </p>

              {/* Price */}
              <div className="space-y-1">
                <div className="flex items-baseline">
                  <span className="text-3xl font-bold tracking-tight">
                    {formatPrice(plan.priceCents, plan.currency)}
                  </span>
                  <span className="text-muted-foreground ml-1">/month</span>
                </div>
              </div>

              {/* Features List */}
              {plan.planFeatures && plan.planFeatures.length > 0 && (
                <div className="space-y-3 pt-4">
                  <h4 className="text-sm font-medium text-foreground">
                    Features included:
                  </h4>
                  <ul className="space-y-2.5">
                    {plan.planFeatures.map((planFeature) => (
                      <li
                        key={planFeature.id}
                        className="flex items-start gap-3 text-sm"
                      >
                        <div className="rounded-full bg-green-100 dark:bg-green-900/20 p-0.5">
                          <Check className="w-3.5 h-3.5 text-green-600 dark:text-green-500" />
                        </div>
                        <span className="text-muted-foreground leading-tight">
                          {planFeature.feature?.name || planFeature.value}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Footer Button */}
            <div className="p-6 pt-0 mt-auto">
              <button
                onClick={() => handleAction(plan)}
                className={cn(
                  'w-full py-2.5 px-4 rounded-md font-medium transition-all duration-200',
                  'border focus:outline-none focus:ring-2 focus:ring-offset-2',
                  isEnterprise
                    ? 'border-primary text-primary hover:bg-primary hover:text-primary-foreground focus:ring-primary'
                    : isPopular
                    ? 'bg-primary text-primary-foreground hover:bg-primary/90 border-primary focus:ring-primary'
                    : 'border-input text-foreground hover:bg-accent hover:text-accent-foreground focus:ring-ring'
                )}
              >
                {isEnterprise ? 'Book a Call' : 'Get Started'}
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
}
