import { Button } from '@b6ai/ui';
import { Input } from '@b6ai/ui';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@b6ai/ui';

export default function Index() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto p-8">
        <div className="space-y-8">
          <div>
            <h1 className="text-4xl font-bold mb-2">
              Welcome to Agent Portal 🚀
            </h1>
            <p className="text-muted-foreground">
              Manage your AI agents with our beautiful UI components
            </p>
          </div>

          {/* Theme Toggle Section */}
          <Card>
            <CardHeader>
              <CardTitle>Theme Demo</CardTitle>
              <CardDescription>
                Toggle between light and dark themes to see the centralized
                theming in action
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <Button
                  onClick={() =>
                    document.documentElement.classList.remove('dark')
                  }
                  variant="outline"
                >
                  Light Theme
                </Button>
                <Button
                  onClick={() => document.documentElement.classList.add('dark')}
                  variant="outline"
                >
                  Dark Theme
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Agent Portal Specific Components */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Agent Management</CardTitle>
                <CardDescription>Manage your AI agents</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Input placeholder="Search agents..." />
                <div className="flex gap-2">
                  <Button size="sm">Create Agent</Button>
                  <Button variant="outline" size="sm">
                    Import
                  </Button>
                  <Button variant="ghost" size="sm">
                    Export
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Portal Actions</CardTitle>
                <CardDescription>Quick portal actions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" variant="secondary">
                  View Dashboard
                </Button>
                <Button className="w-full" variant="outline">
                  Agent Analytics
                </Button>
                <Button className="w-full" variant="ghost">
                  Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
