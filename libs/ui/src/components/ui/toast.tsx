import React, { useEffect } from 'react';
import { CheckCircle, AlertCircle, XCircle, Info, X } from 'lucide-react';
export type ToastType =
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'gradient'
  | 'custom';
interface ToastProps {
  type: ToastType;
  message: string;
  onClose: () => void;
  duration?: number;
  customClass?: string;
}
export const Toast: React.FC<ToastProps> = ({
  type,
  message,
  onClose,
  duration = 5000,
  customClass,
}) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, duration);
    return () => clearTimeout(timer);
  }, [duration, onClose]);
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle size={20} className="text-green-500" />;
      case 'error':
        return <XCircle size={20} className="text-red-500" />;
      case 'warning':
        return <AlertCircle size={20} className="text-amber-500" />;
      case 'info':
      case 'gradient':
      case 'custom':
        return (
          <Info size={20} className="text-b6ai-blue dark:text-b6ai-cyan" />
        );
      default:
        return null;
    }
  };
  const getBgColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'error':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'warning':
        return 'bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800';
      case 'info':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      case 'gradient':
        return 'bg-gradient-to-r from-b6ai-cyan via-b6ai-blue to-b6ai-navy text-white border-transparent';
      case 'custom':
        return customClass || 'bg-light-card dark:bg-dark-card';
      default:
        return 'bg-light-card dark:bg-dark-card';
    }
  };
  const getTextColor = () => {
    return type === 'gradient' ? 'text-white' : '';
  };
  return (
    <div
      className={`flex items-center p-3 rounded-lg shadow-md border ${getBgColor()} min-w-[300px] max-w-md ${getTextColor()}`}
      role="alert"
    >
      <div className="flex-shrink-0 mr-3">{getIcon()}</div>
      <div className="flex-1 mr-2 text-sm">{message}</div>
      <button
        onClick={onClose}
        className="p-1 rounded-full hover:bg-light-muted/20 dark:hover:bg-dark-muted/20 transition-colors"
      >
        <X
          size={16}
          className={
            type === 'gradient'
              ? 'text-white/80'
              : 'text-light-muted-foreground dark:text-dark-muted-foreground'
          }
        />
      </button>
    </div>
  );
};
