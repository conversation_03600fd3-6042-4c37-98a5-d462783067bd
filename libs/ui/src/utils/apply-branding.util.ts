export interface ThemeConfig {
  background: string;
  foreground: string;
  primary: string;
  secondary: string;
  accent: string;
}

export interface Branding {
  light: ThemeConfig;
  dark: ThemeConfig;
}

function hexToHsl(hex: string): string {
  let r = 0,
    g = 0,
    b = 0;

  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  } else if (hex.length === 7) {
    r = parseInt(hex.slice(1, 3), 16);
    g = parseInt(hex.slice(3, 5), 16);
    b = parseInt(hex.slice(5, 7), 16);
  }

  r /= 255;
  g /= 255;
  b /= 255;

  const max = Math.max(r, g, b),
    min = Math.min(r, g, b);
  let h = 0,
    s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(
    l * 100
  )}%`;
}

export function applyBranding(
  branding: Branding,
  mode: 'light' | 'dark' = 'light'
) {
  if (typeof document === 'undefined') return; // skip if SSR

  const theme = branding[mode];
  const root = document.documentElement;

  const setVar = (name: string, value: string) => {
    root.style.setProperty(name, hexToHsl(value));
  };

  setVar('--background', theme.background);
  setVar('--foreground', theme.foreground);
  setVar('--primary', theme.primary);
  setVar('--secondary', theme.secondary);
  setVar('--accent', theme.accent);
}
