/**
 * Centralized translation keys for the application
 * All text in the application should use these constants instead of hardcoded strings
 */
export const I18N_KEYS = {
  // Common
  COMMON: {
    SAVE: 'common.save',
    CANCEL: 'common.cancel',
    DELETE: 'common.delete',
    EDIT: 'common.edit',
    CONFIRM: 'common.confirm',
    BACK: 'common.back',
    NEXT: 'common.next',
    LOADING: 'common.loading',
    SUCCESS: 'common.success',
    ERROR: 'common.error',
    WARNING: 'common.warning',
    INFO: 'common.info',
    CONTINUE: 'common.continue',
    COMPLETE_SETUP: 'common.completeSetup',
    YES: 'common.yes',
    NO: 'common.no',
    ALL_RIGHTS_RESERVED: 'common.allRightsReserved',
  },
  // Auth
  AUTH: {
    LOGOUT: 'auth.logout',
    LOGOUT_CONFIRM: 'auth.logoutConfirm',
    LOGOUT_SUCCESS: 'auth.logoutSuccess',
    LOGIN_TITLE: 'auth.loginTitle',
    LOGIN_DESCRIPTION: 'auth.loginDescription',
    LOGIN_BUTTON: 'auth.loginButton',
    EMAIL: 'auth.email',
    EMAIL_PLACEHOLDER: 'auth.emailPlaceholder',
    PASSWORD: 'auth.password',
    PASSWORD_PLACEHOLDER: 'auth.passwordPlaceholder',
    EMAIL_REQUIRED: 'auth.emailRequired',
    PASSWORD_REQUIRED: 'auth.passwordRequired',
    INVALID_EMAIL: 'auth.invalidEmail',
    INVALID_CREDENTIALS: 'auth.invalidCredentials',
    FORGOT_PASSWORD: 'auth.forgotPassword',
    FORGOT_PASSWORD_TITLE: 'auth.forgotPasswordTitle',
    FORGOT_PASSWORD_DESCRIPTION: 'auth.forgotPasswordDescription',
    RESET_PASSWORD: 'auth.resetPassword',
    RESET_EMAIL_SENT: 'auth.resetEmailSent',
    RESET_EMAIL_INSTRUCTIONS: 'auth.resetEmailInstructions',
    BACK_TO_LOGIN: 'auth.backToLogin',
    REMEMBER_ME: 'auth.rememberMe',
    NO_ACCOUNT: 'auth.noAccount',
    SIGN_UP: 'auth.signUp',
  },
  // Navigation
  NAV: {
    DASHBOARD: 'nav.dashboard',
    CHATS: 'nav.chats',
    BOTS: 'nav.bots',
    TEAMS: 'nav.teams',
    ACTIVITY_LOGS: 'nav.activityLogs',
    SETTINGS: 'nav.settings',
    LOGOUT: 'nav.logout',
  },
  // Settings
  SETTINGS: {
    TITLE: 'settings.title',
    DESCRIPTION: 'settings.description',
    GENERAL: 'settings.general',
    GENERAL_DESC: 'settings.generalDesc',
    BUSINESS_HOURS: 'settings.businessHours',
    BUSINESS_HOURS_DESC: 'settings.businessHoursDesc',
    ROUTING_RULES: 'settings.routingRules',
    ROUTING_RULES_DESC: 'settings.routingRulesDesc',
    QUEUE_SLA: 'settings.queueSla',
    QUEUE_SLA_DESC: 'settings.queueSlaDesc',
    NOTIFICATIONS: 'settings.notifications',
    NOTIFICATIONS_DESC: 'settings.notificationsDesc',
    SECURITY: 'settings.security',
    SECURITY_DESC: 'settings.securityDesc',
    INTEGRATIONS: 'settings.integrations',
    INTEGRATIONS_DESC: 'settings.integrationsDesc',
    REPORTING: 'settings.reporting',
    REPORTING_DESC: 'settings.reportingDesc',
    BILLING: 'settings.billing',
    BILLING_DESC: 'settings.billingDesc',
    BRANDING: 'settings.branding',
    BRANDING_DESC: 'settings.brandingDesc',
    SAVE_CHANGES: 'settings.saveChanges',
    CHANGES_SAVED: 'settings.changesSaved',
  },
  // Notifications
  NOTIFICATIONS: {
    NEW_MESSAGE: 'notifications.newMessage',
    CHAT_ASSIGNED: 'notifications.chatAssigned',
    SLA_BREACH: 'notifications.slaBreach',
    SYSTEM_ALERT: 'notifications.systemAlert',
    VIEW_ALL: 'notifications.viewAll',
    NO_NOTIFICATIONS: 'notifications.noNotifications',
  },
  // Onboarding
  ONBOARDING: {
    TITLE: 'onboarding.title',
    ORGANIZATION_STEP: 'onboarding.organizationStep',
    BILLING_STEP: 'onboarding.billingStep',
    TEAM_STEP: 'onboarding.teamStep',
    ORG_NAME: 'onboarding.orgName',
    INDUSTRY: 'onboarding.industry',
    ORG_SIZE: 'onboarding.orgSize',
    WEBSITE: 'onboarding.website',
    PAYMENT_DETAILS: 'onboarding.paymentDetails',
    BILLING_ADDRESS: 'onboarding.billingAddress',
    INVITE_TEAM: 'onboarding.inviteTeam',
    INVITE_DESCRIPTION: 'onboarding.inviteDescription',
  },
  // Teams
  TEAMS: {
    TITLE: 'teams.title',
    AGENTS: 'teams.agents',
    DEPARTMENTS: 'teams.departments',
    CREATE_AGENT: 'teams.createAgent',
    CREATE_DEPARTMENT: 'teams.createDepartment',
    FIRST_NAME: 'teams.firstName',
    LAST_NAME: 'teams.lastName',
    EMAIL: 'teams.email',
    DEPARTMENT: 'teams.department',
    PHONE: 'teams.phone',
    ENTER_FIRST_NAME: 'teams.enterFirstName',
    ENTER_LAST_NAME: 'teams.enterLastName',
    ENTER_EMAIL: 'teams.enterEmail',
    SELECT_DEPARTMENT: 'teams.selectDepartment',
    ENTER_PHONE: 'teams.enterPhone',
    FIRST_NAME_REQUIRED: 'teams.firstNameRequired',
    LAST_NAME_REQUIRED: 'teams.lastNameRequired',
    EMAIL_REQUIRED: 'teams.emailRequired',
    INVALID_EMAIL: 'teams.invalidEmail',
    DEPARTMENT_REQUIRED: 'teams.departmentRequired',
    DEPARTMENT_NAME: 'teams.departmentName',
    ENTER_DEPARTMENT_NAME: 'teams.enterDepartmentName',
    DEPARTMENT_NAME_REQUIRED: 'teams.departmentNameRequired',
  },
  // Bots
  BOTS: {
    TITLE: 'bots.title',
    CREATE_BOT: 'bots.createBot',
    CREATE_DESCRIPTION: 'bots.createDescription',
    DESIGN_TAB: 'bots.designTab',
    TRAIN_TAB: 'bots.trainTab',
    FAQ_TAB: 'bots.faqTab',
    SETTINGS_TAB: 'bots.settingsTab',
    SETTINGS_DESCRIPTION: 'bots.settingsDescription',
    SAVE_BOT: 'bots.saveBot',
  },
  // Chat
  CHAT: {
    TITLE: 'chat.title',
  },
  // Languages
  LANGUAGES: {
    SELECT: 'languages.select',
    ENGLISH: 'languages.english',
    ARABIC: 'languages.arabic',
    SPANISH: 'languages.spanish',
    FRENCH: 'languages.french',
    GERMAN: 'languages.german',
    PORTUGUESE: 'languages.portuguese',
    CHINESE: 'languages.chinese',
    JAPANESE: 'languages.japanese',
  },
  // Modals
  MODALS: {
    DELETE_TITLE: 'modals.deleteTitle',
    DELETE_DESCRIPTION: 'modals.deleteDescription',
    SUCCESS_TITLE: 'modals.successTitle',
    SUCCESS_DESCRIPTION: 'modals.successDescription',
  },
  // Toasts
  TOASTS: {
    SETTINGS_SAVED: 'toasts.settingsSaved',
    OPERATION_FAILED: 'toasts.operationFailed',
    BOT_CREATED: 'toasts.botCreated',
    BOT_SAVED: 'toasts.botSaved',
    TEAM_INVITED: 'toasts.teamInvited',
    AGENT_CREATED: 'toasts.agentCreated',
    DEPARTMENT_CREATED: 'toasts.departmentCreated',
    INTEGRATION_CONFIGURED: 'toasts.integrationConfigured',
    INTEGRATION_CONNECTED: 'toasts.integrationConnected',
    INTEGRATION_DISCONNECTED: 'toasts.integrationDisconnected',
  },
};
