import type { Middleware } from '@reduxjs/toolkit';

export const loggerMiddleware: Middleware =
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (store) => (next) => (action: any) => {
    if (process.env['NODE_ENV'] === 'development') {
      console.group(action.type);
      console.info('dispatching', action);
      const result = next(action);
      console.log('next state', store.getState());
      console.groupEnd();
      return result;
    }
    return next(action);
  };
