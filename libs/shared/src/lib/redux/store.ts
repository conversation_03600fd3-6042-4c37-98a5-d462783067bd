import { configureStore } from '@reduxjs/toolkit';
import { apiSlice } from './api/apiSlice';
import { apiErrorMiddleware } from './middleware/api.middleware';
import { loggerMiddleware } from './middleware/logger.middleware';
import { authReducer } from './slices/auth.slice';
import { uiReducer } from './slices/ui.slice';

export const store = configureStore({
  reducer: {
    [apiSlice.reducerPath]: apiSlice.reducer,
    auth: authReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(apiSlice.middleware)
      .concat(apiErrorMiddleware)
      .concat(loggerMiddleware),
});

export type AppStore = typeof store;
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
