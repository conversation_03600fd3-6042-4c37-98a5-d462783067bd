import { SoftDelete } from './base.type';

export enum PlanType {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export interface Feature extends SoftDelete {
  id: string;
  featureCode?: string;
  name: string;
  description: string;
}

export interface PlanFeature {
  id: string;
  value: string;
  featureId: string;
  planId: string;
  feature: Feature;
  plan: string;
}

export interface Plan extends SoftDelete {
  id: string;
  planCode: string;
  name: string;
  description: string;
  priceCents: number;
  currency: string;
  gatewayName: string;
  gatewayRefId: string;
  planFeatures: PlanFeature[];
}
