{"name": "@b6-ai-ui/source", "version": "0.0.0", "license": "MIT", "scripts": {"dev:e-admin": "nx serve enterprise-admin"}, "private": true, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "next": "~15.2.4", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@next/eslint-plugin-next": "^15.2.4", "@nx/cypress": "21.4.1", "@nx/eslint": "21.4.1", "@nx/eslint-plugin": "21.4.1", "@nx/jest": "21.4.1", "@nx/js": "21.4.1", "@nx/next": "21.4.1", "@nx/react": "21.4.1", "@nx/vite": "21.4.1", "@nx/web": "21.4.1", "@nx/workspace": "21.4.1", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/jest": "^30.0.0", "@types/node": "20.19.9", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "^3.0.0", "ajv": "^8.0.0", "autoprefixer": "10.4.13", "babel-jest": "^30.0.2", "cypress": "^14.2.1", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.0.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "i18next": "^25.5.2", "i18next-browser-languagedetector": "^8.2.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jest-util": "^30.0.2", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "21.4.1", "postcss": "8.4.38", "prettier": "^2.6.2", "react-i18next": "^15.7.3", "tailwindcss": "3.4.3", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vitest": "^3.0.0"}, "workspaces": ["apps/*", "libs/*"]}